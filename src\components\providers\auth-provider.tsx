"use client"

import { createContext, useContext, useEffect, useState } from "react"
import { authClient, type Session, type User } from "@/lib/auth/client"

interface AuthContextType {
  session: Session | null
  user: User | null
  isLoading: boolean
  signIn: typeof authClient.signIn
  signUp: typeof authClient.signUp
  signOut: typeof authClient.signOut
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [session, setSession] = useState<Session | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const getSession = async () => {
      try {
        const sessionData = await authClient.getSession()
        setSession(sessionData.data)
      } catch (error) {
        console.error("Failed to get session:", error)
      } finally {
        setIsLoading(false)
      }
    }

    getSession()
  }, [])

  const value: AuthContextType = {
    session,
    user: session?.user || null,
    isLoading,
    signIn: authClient.signIn,
    signUp: authClient.signUp,
    signOut: authClient.signOut,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
