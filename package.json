{"name": "magneto-audit-management", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 8080 --hostname localhost", "build": "next build", "start": "next start --port 8080 --hostname localhost", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@prisma/client": "^5.22.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.59.0", "better-auth": "^1.2.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.460.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "tailwind-merge": "^2.6.0", "zod": "^3.25.67", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "prisma": "^5.22.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}