// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlserver"
  url      = env("DATABASE_URL")
}

// User model for authentication and user management
model User {
  id          String   @id @default(cuid())
  email       String   @unique
  name        String?
  password    String?
  role        String   @default("USER") // SUPER_ADMIN, ADMIN, MANAGER, AUDITOR, USER
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  lastLoginAt DateTime?

  // Relations
  organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  organizationId String?

  // Audit relations
  auditorsOn     AuditUser[]
  createdAudits  Audit[]     @relation("AuditCreator")

  // Report relations
  createdReports Report[]    @relation("ReportCreator")

  // Action relations
  assignedActions Action[]   @relation("ActionAssignee")
  createdActions  Action[]   @relation("ActionCreator")

  @@map("users")
}

// Organization model for multi-tenant support
model Organization {
  id          String   @id @default(cuid())
  name        String
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  users   User[]
  audits  Audit[]
  reports Report[]

  @@map("organizations")
}

// Audit model for audit management
model Audit {
  id          String   @id @default(cuid())
  title       String
  description String?
  status      String   @default("PLANNED") // PLANNED, IN_PROGRESS, COMPLETED, CANCELLED
  startDate   DateTime
  endDate     DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  organizationId String

  creator   User   @relation("AuditCreator", fields: [creatorId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  creatorId String

  // Audit team
  auditors    AuditUser[]

  // Audit content
  observations Observation[]
  actions      Action[]
  reports      Report[]

  @@map("audits")
}

// Junction table for audit team members
model AuditUser {
  id     String @id @default(cuid())
  role   String @default("AUDITOR")

  audit   Audit  @relation(fields: [auditId], references: [id], onDelete: Cascade)
  auditId String

  user   User   @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  userId String

  @@unique([auditId, userId])
  @@map("audit_users")
}

// Observation model for audit findings
model Observation {
  id          String   @id @default(cuid())
  title       String
  description String
  severity    String   @default("LOW") // CRITICAL, HIGH, MEDIUM, LOW
  status      String   @default("OPEN") // OPEN, IN_PROGRESS, RESOLVED, CLOSED
  evidence    String?  // JSON field for storing evidence files/links
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  audit   Audit  @relation(fields: [auditId], references: [id], onDelete: Cascade)
  auditId String

  actions Action[]

  @@map("observations")
}

// Action model for corrective actions
model Action {
  id          String   @id @default(cuid())
  title       String
  description String
  dueDate     DateTime
  status      String   @default("PENDING") // PENDING, IN_PROGRESS, COMPLETED, CANCELLED
  priority    String   @default("MEDIUM") // CRITICAL, HIGH, MEDIUM, LOW
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  completedAt DateTime?

  // Relations
  audit   Audit  @relation(fields: [auditId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  auditId String

  observation   Observation? @relation(fields: [observationId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  observationId String?

  assignee   User   @relation("ActionAssignee", fields: [assigneeId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  assigneeId String

  creator   User   @relation("ActionCreator", fields: [creatorId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  creatorId String

  @@map("actions")
}

// Report model for audit reports
model Report {
  id          String       @id @default(cuid())
  title       String
  content     String       // JSON field for rich content
  status      ReportStatus @default(DRAFT)
  version     Int          @default(1)
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  publishedAt DateTime?

  // Relations
  organization   Organization @relation(fields: [organizationId], references: [id])
  organizationId String
  
  audit   Audit  @relation(fields: [auditId], references: [id])
  auditId String
  
  creator   User   @relation("ReportCreator", fields: [creatorId], references: [id])
  creatorId String

  @@map("reports")
}

// Enums
enum UserRole {
  SUPER_ADMIN
  ADMIN
  MANAGER
  AUDITOR
  USER
}

enum AuditStatus {
  PLANNED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum ObservationSeverity {
  CRITICAL
  HIGH
  MEDIUM
  LOW
}

enum ObservationStatus {
  OPEN
  IN_PROGRESS
  RESOLVED
  CLOSED
}

enum ActionStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum ActionPriority {
  CRITICAL
  HIGH
  MEDIUM
  LOW
}

enum ReportStatus {
  DRAFT
  REVIEW
  APPROVED
  PUBLISHED
}
