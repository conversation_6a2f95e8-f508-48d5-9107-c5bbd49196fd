generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlserver"
  url      = env("DATABASE_URL")
}

model User {
  id          String   @id @default(cuid())
  email       String   @unique
  name        String?
  password    String?
  role        String   @default("USER")
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  lastLoginAt DateTime?

  organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  organizationId String?

  auditorsOn     AuditUser[]
  createdAudits  Audit[]     @relation("AuditCreator")
  createdReports Report[]    @relation("ReportCreator")
  assignedActions Action[]   @relation("ActionAssignee")
  createdActions  Action[]   @relation("ActionCreator")

  @@map("users")
}

model Organization {
  id          String   @id @default(cuid())
  name        String
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  users   User[]
  audits  Audit[]
  reports Report[]

  @@map("organizations")
}

model Audit {
  id          String   @id @default(cuid())
  title       String
  description String?
  status      String   @default("PLANNED")
  startDate   DateTime
  endDate     DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  organizationId String

  creator   User   @relation("AuditCreator", fields: [creatorId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  creatorId String

  auditors    AuditUser[]
  observations Observation[]
  actions      Action[]
  reports      Report[]

  @@map("audits")
}

model AuditUser {
  id     String @id @default(cuid())
  role   String @default("AUDITOR")

  audit   Audit  @relation(fields: [auditId], references: [id], onDelete: Cascade)
  auditId String

  user   User   @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  userId String

  @@unique([auditId, userId])
  @@map("audit_users")
}

model Observation {
  id          String   @id @default(cuid())
  title       String
  description String
  severity    String   @default("LOW")
  status      String   @default("OPEN")
  evidence    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  audit   Audit  @relation(fields: [auditId], references: [id], onDelete: Cascade)
  auditId String

  actions Action[]

  @@map("observations")
}

model Action {
  id          String   @id @default(cuid())
  title       String
  description String
  dueDate     DateTime
  status      String   @default("PENDING")
  priority    String   @default("MEDIUM")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  completedAt DateTime?

  audit   Audit  @relation(fields: [auditId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  auditId String

  observation   Observation? @relation(fields: [observationId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  observationId String?

  assignee   User   @relation("ActionAssignee", fields: [assigneeId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  assigneeId String

  creator   User   @relation("ActionCreator", fields: [creatorId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  creatorId String

  @@map("actions")
}

model Report {
  id          String   @id @default(cuid())
  title       String
  content     String
  status      String   @default("DRAFT")
  version     Int      @default(1)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  publishedAt DateTime?

  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  organizationId String

  audit   Audit  @relation(fields: [auditId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  auditId String

  creator   User   @relation("ReportCreator", fields: [creatorId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  creatorId String

  @@map("reports")
}